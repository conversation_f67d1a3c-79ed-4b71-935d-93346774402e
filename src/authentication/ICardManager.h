#ifndef ICARDMANAGER_H
#define ICARDMANAGER_H

#include <Arduino.h>

/**
 * 卡片管理器接口
 * 定义卡片管理操作的通用接口，与认证功能分离
 */
class ICardManager {
public:
    virtual ~ICardManager() = default;
    
    /**
     * 注册新卡片
     * @return 注册是否成功启动
     */
    virtual bool registerNewCard() = 0;
    
    /**
     * 删除指定卡片（仅从数据库删除）
     * @param uid 卡片UID
     * @return 删除是否成功
     */
    virtual bool deleteCard(const String& uid) = 0;
    
    /**
     * 擦除并删除指定卡片（物理擦除+数据库删除）
     * @param uid 卡片UID
     * @return 擦除删除是否成功启动
     */
    virtual bool eraseAndDeleteCard(const String& uid) = 0;
    
    /**
     * 列出所有已注册的卡片
     */
    virtual void listRegisteredCards() = 0;
    
    /**
     * 检查是否有管理操作正在进行
     * @return 是否有操作正在进行
     */
    virtual bool hasOngoingOperation() = 0;
    
    /**
     * 处理管理操作（在主循环中调用）
     */
    virtual void handleOperations() = 0;
    
    /**
     * 重置管理器状态
     */
    virtual void reset() = 0;
};

#endif // ICARDMANAGER_H
